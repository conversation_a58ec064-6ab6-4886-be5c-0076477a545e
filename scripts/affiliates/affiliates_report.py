import csv
import io
import json

from utils.config import ENV
from utils.LoggerService import logger_service
from utils.settings_manager import SettingsManagerService


class AffiliatesReport:
    def __init__(self):
        self.xlsx_obj = "scripts/affiliates/Affiliates Tables CTS EXpresslane.csv"

    def read_excel_obj(self) -> list[dict]:
        """
        Reads a CSV file using Python's built-in CSV module, skipping the first 3 lines,
        and converts all "N/A" values to bool False.
        return:
            List of dicts
        """
        with open(self.xlsx_obj, mode="r", encoding="utf-8") as file_stream:
            csv_reader = csv.reader(file_stream)

            # Skip the first three lines
            for _ in range(2):
                next(csv_reader, None)

            # Read headers
            headers = next(csv_reader, [])

            # Read data and process "N/A" values
            data_dict = [
                {
                    header: (False if value == "N/A" else value)
                    for header, value in zip(headers, row)
                }
                for row in csv_reader
            ]

        return data_dict

    def convert_to_bool(self, value: str) -> bool:
        """
        Convert string representations of booleans to actual boolean values while leaving other types unchanged.
        If the value is NaN, it is replaced with None.
        This function handles strings like "true" or "false" (case-insensitive) and returns the corresponding boolean value.
        If the input is not a string, or if it does not represent a boolean, the function returns the value as-is.
        """

        if isinstance(value, str):
            value_lower = value.strip().lower()
            if value_lower == "true":
                return True
            elif value_lower == "false":
                return False

        return value

    def transform_keys_from_xlsx_file_to_dynamo_table_format(
        self, mapping_affiliate_keys: dict
    ) -> list:
        """
        Transforms the keys of each dictionary in the data read from an Excel file
        into the appropriate format for a DynamoDB table.

        This function:
        1. Reads data from the Excel object.
        2. For each record, it transforms the keys based on a predefined mapping (`mapping_affiliate_keys`).
        3. Converts the corresponding values to boolean where necessary using the `convert_to_bool` method.

        Returns:
            list: A list of dictionaries with transformed keys and converted values.
        """

        data_dict = self.read_excel_obj()

        return [
            {
                mapping_affiliate_keys.get(k, k): self.convert_to_bool(v)
                for k, v in d.items()
            }
            for d in data_dict
        ]

    def filter_data_by_config_table(self, data: dict, affiliates: dict) -> dict:
        """
        Filter from xlsx Affiliate Name to configuration_rules.rules DynamoDB table id 1 express lane
        Retrun dict
        """
        return {
            key: affiliates[key]
            for key in affiliates
            if key in {item["Affiliate Name"] for item in data}
        }

    def compare_and_report_data(
        self, updated_data_dict: dict, filtered_affiliate: dict
    ) -> dict:
        """
        Compares the updated data with the expected configuration for each affiliate
        and reports discrepancies between the actual and expected values.

        Args:
            updated_data_dict (list): A list of dictionaries representing the updated data.
            filtered_affiliate (dict): A dictionary containing the expected configuration for each affiliate.

        Returns:
            dict: This function does not return anything directly but logs warnings when discrepancies are found.
        """
        result = {}

        # Using a set for faster lookup instead of iterating over a dictionary.
        filtered_affiliate_set = set(filtered_affiliate.keys())

        for entry in updated_data_dict:
            affiliate_name = entry["Affiliate Name"]

            # This optimizes the loop by avoiding unnecessary checks.
            if affiliate_name not in filtered_affiliate_set:
                continue

            expected_config = filtered_affiliate[affiliate_name]

            result[affiliate_name] = {
                key: {
                    "Affiliate_config_table": expected_value,
                    "Affiliate_config_file": entry.get(key, None),
                }
                for key, expected_value in expected_config.items()
            }

            for key, expected_value in expected_config.items():
                # Retrieve the actual value from the updated data for the current key
                actual_value = entry.get(key, None)
                if key in [
                    "create_attributes",
                    "skip_content",
                    "remove_attributes",
                ] and isinstance(actual_value, str):
                    actual_value = actual_value.replace("True", "true")
                    # Ensure proper JSON formatting before parsing
                    actual_value = json.loads(actual_value)

                if expected_value != actual_value and expected_value != []:
                    logger_service.warning(
                        "*** REPORT ISSUE: Affiliate_config_table: %s - value: %s - Key: %s - Affiliate_config_file: %s is expected ***",
                        affiliate_name,
                        expected_value,
                        key,
                        actual_value,
                    )

        return result


if __name__ == "__main__":
    mapping_affiliate_keys = {
        "Create Attributes": "create_attributes",
        "Delivery name": "delivery_name",
        "Faspex": "faspex",
        "Has HD": "has_HD",
        "Has SD": "has_SD",
        "HD Movies Category": "HD_movies_category",
        "HD Series Category": "HD_series_category",
        "PCT": "pct",
        "Product Code": "product",
        "Provider": "provider",
        "Removed Fields": "remove_attributes",
        "SD Movies Category": "SD_movies_category",
        "SD Series Category": "SD_series_category",
        "Skip Content": "skip_content",
        "TAR": "tar",
    }
    settings_service = SettingsManagerService(f"tacdev-event-config-{ENV}")
    settings_service.initialize("express-lane")
    event_settings = settings_service.settings

    config_rules = event_settings.configuration_rules.get("config", {})
    affiliates = event_settings.configuration_rules.get("rules", {})

    affiliate_report = AffiliatesReport()

    updated_data_dict = (
        affiliate_report.transform_keys_from_xlsx_file_to_dynamo_table_format(
            mapping_affiliate_keys
        )
    )
    filtered_affiliate = affiliate_report.filter_data_by_config_table(
        updated_data_dict, affiliates
    )
    affiliate_report.compare_and_report_data(updated_data_dict, filtered_affiliate)
