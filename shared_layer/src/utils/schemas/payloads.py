from typing import Any, Dict, List, Literal, Optional

from pydantic import AliasPath, BaseModel, ConfigDict, Field, model_validator

from ..LoggerService import logger_service


class WonderlandSidecarSchema(BaseModel):
    """Schema for Wonderland sidecar JSON payload."""

    model_config = ConfigDict(extra="ignore")

    version: int
    sidecar_id: str
    asset_version: str
    asset_format: Literal["single_file", "multi_file"]
    asset_id: str
    reach_package_id: str


class BebanjoPayloadSchema(BaseModel):
    """Schema for Bebanjo payload JSON that maps to RASCL adi_json structure."""

    model_config = ConfigDict(extra="ignore", frozen=True)

    # Program identification fields
    network: str = Field(..., serialization_alias="network")
    show_title: str = Field(
        ..., validation_alias="showTitle", serialization_alias="content_name"
    )
    season_number: Optional[int] = Field(
        None, validation_alias="seasonNumber", serialization_alias="season_num"
    )
    episode_number: Optional[int] = Field(
        None,
        validation_alias=AliasPath("episodeID", "episodeNumber"),
        serialization_alias="episode_number",
    )
    episode_name: Optional[str] = Field(
        None, validation_alias="episodeName", serialization_alias="episode_title"
    )

    # Program type and classification
    show_type: str = Field(
        ..., validation_alias="showType", serialization_alias="program_type"
    )
    asset_type: Optional[str] = Field(
        None, validation_alias="assetType", serialization_alias="episode_category"
    )

    # Identifiers
    unique_id: str = Field(
        ..., validation_alias="uniqueId", serialization_alias="material_id"
    )
    traffic_code: Optional[str] = Field(
        None,
        validation_alias="trafficCode",
        serialization_alias="episode_production_number",
    )
    radar_product_id: Optional[str] = Field(
        None, validation_alias="radarProductId", serialization_alias="radar_group_id"
    )

    # Content metadata
    summary_short: Optional[str] = Field(
        None,
        validation_alias="summaryShort",
        serialization_alias="episode_short_synopsis",
    )
    one_line_description: Optional[str] = Field(
        None,
        validation_alias="oneLineDescription",
        serialization_alias="episode_long_synopsis",
    )
    rating: Optional[str] = Field(None, serialization_alias="default_rating")
    genres: Optional[str] = Field(None, serialization_alias="genre_code")
    actors: Optional[str] = Field(None, serialization_alias="actors_raw")

    # Dates and years
    year: Optional[int] = Field(None, serialization_alias="content_start_year")
    air_date: Optional[str] = Field(
        None, validation_alias="airDate", serialization_alias="episode_airdate"
    )

    # Technical details
    display_run_time: Optional[str] = Field(None, validation_alias="displayRunTime")
    closed_captioning: Optional[str] = Field(None, validation_alias="closedCaptioning")
    hd_content: Optional[str] = Field(
        None, validation_alias="hdContent", serialization_alias="is_4k"
    )
    audio_type: Optional[str] = Field(None, validation_alias="audioType")
    languages: Optional[str] = Field(None)

    # VOD and window information
    vod_title: Optional[str] = Field(None, validation_alias="vodTitle")
    vod_short_title: Optional[str] = Field(
        None, validation_alias="vodShortTitle", serialization_alias="title_brief"
    )
    licensing_window_start: Optional[str] = Field(
        None,
        validation_alias="licensingWindowStart",
        serialization_alias="episode_window_start",
    )
    licensing_window_end: Optional[str] = Field(
        None,
        validation_alias="licensingWindowEnd",
        serialization_alias="episode_window_end",
    )

    # Categorization
    cmc_categories: Optional[List[str]] = Field(
        None, validation_alias="cmcCategories", serialization_alias="hd_mappings"
    )

    # File information
    video_file_name: Optional[str] = Field(
        None, validation_alias="videoFileName", serialization_alias="source_filename"
    )
    xml_file_name: Optional[str] = Field(None, validation_alias="xmlFileName")

    @model_validator(mode="before")
    def process_show_type(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Transform showType values to match expected program_type values."""
        show_type = values.get("showType", "")
        if show_type:
            if show_type.lower() == "tvshow":
                values["showType"] = "series"
            elif show_type.lower() == "movie":
                values["showType"] = "movie"
            elif show_type.lower() == "special":
                values["showType"] = "special"
        return values

    @model_validator(mode="before")
    def process_hd_content(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Transform hdContent to boolean for is_4k field."""
        hd_content = values.get("hdContent", "")
        if hd_content:
            values["hdContent"] = hd_content.upper() == "Y"
        return values

    @model_validator(mode="before")
    def set_defaults(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Set default values for required RASCL fields."""
        # Set default country
        if "country" not in values:
            values["country"] = "USA"

        # Set default content type
        if "content_type" not in values:
            values["content_type"] = "Long Form"

        # Set default C-Type and D-Type values
        if "episode_c_type" not in values:
            values["episode_c_type"] = "C3"

        if "episode_d_type" not in values:
            values["episode_d_type"] = "D4"

        return values

    @model_validator(mode="after")
    def log_field_issues(self) -> "BebanjoPayloadSchema":
        """Log warnings for fields that are blank, empty, or exceed length limits."""
        for field_name, field_value in self:
            if isinstance(field_value, str):
                if not field_value.strip():
                    logger_service.warning(
                        f"Bebanjo Validation Warning: Field '{field_name}' is blank or empty."
                    )
                elif len(field_value) > 34:
                    logger_service.warning(
                        f"Bebanjo Validation Warning: Field '{field_name}' has more than 34 characters "
                        f"(length: {len(field_value)}). Value: '{field_value[:34]}...'"
                    )
            elif field_value is None:
                logger_service.warning(
                    f"Bebanjo Validation Warning: Field '{field_name}' is blank or empty."
                )
        return self

    def _set_adi_defaults(
        self, adi_json: Dict[str, Dict[str, Any]], dump: Dict[str, Any]
    ) -> None:
        """Set default values for fields required by ADI JSON."""
        default_fields = {
            "episode_tms_id": "",
            "tms_id": "",
            "content_prefix": "",
            "comscore_c6": "",
            "copyright": dump.get("network", ""),
            "rating_content_labels": "",
            "country": "USA",
            "content_type": "Long Form",
            "episode_c_type": "C3",
            "episode_d_type": "D4",
            "cms_folder_name": "",
            "show_folder": "",
            "sd_mappings": "",
            "ad_content_id": "",
            "movie_asset_id": "",
            "categories_and_distribution": "",
            "reach_genre": "",
            "content_short_synopsis": "",
            "content_long_synopsis": "",
            "season_short_synopsis": "",
            "season_long_synopsis": "",
            "keywords": "",
        }
        for field, default_value in default_fields.items():
            adi_json.setdefault(field, {"value": default_value})

    def _process_actors(self, adi_json: Dict[str, Dict[str, Any]]) -> None:
        """Parse actor string and convert to structured list."""
        if "actors_raw" not in adi_json:
            return

        actors_string = adi_json.pop("actors_raw")["value"]
        if not actors_string:
            return

        actors_list = []
        # Assumes a flat list of "Last, First, Last, First, ..."
        name_parts = [p.strip() for p in actors_string.split(",") if p.strip()]

        if len(name_parts) > 1 and len(name_parts) % 2 == 0:
            for i in range(0, len(name_parts), 2):
                actors_list.append(
                    {"first": name_parts[i + 1], "last": name_parts[i], "character": ""}
                )
        else:  # Fallback to original logic for other formats
            for actor in actors_string.split(", "):
                if not actor.strip():
                    continue
                parts = [p.strip() for p in actor.strip().split(", ")]
                if len(parts) >= 2:
                    actors_list.append(
                        {"first": parts[1], "last": parts[0], "character": ""}
                    )
                else:
                    full_name = actor.strip().split(" ")
                    if len(full_name) >= 2:
                        actors_list.append(
                            {
                                "first": " ".join(full_name[:-1]),
                                "last": full_name[-1],
                                "character": "",
                            }
                        )
        if actors_list:
            adi_json["actors"] = {"value": actors_list}

    def _process_synopsis(
        self, adi_json: Dict[str, Dict[str, Any]], dump: Dict[str, Any]
    ) -> None:
        """Set default synopsis values if they are not present."""
        if not adi_json.get("content_short_synopsis", {}).get("value"):
            adi_json["content_short_synopsis"]["value"] = adi_json.get(
                "episode_short_synopsis", {}
            ).get("value", f"Presented by {dump.get('network', '')}")

        if not adi_json.get("content_long_synopsis", {}).get("value"):
            adi_json["content_long_synopsis"]["value"] = adi_json.get(
                "episode_long_synopsis", {}
            ).get("value", f"Presented by {dump.get('network', '')}")

    def _map_reach_genre(self, adi_json: Dict[str, Dict[str, Any]]) -> None:
        """Map genre_code to a valid Reach genre."""
        genre_code = adi_json.get("genre_code", {}).get("value", "")
        if not genre_code or adi_json.get("reach_genre", {}).get("value"):
            return

        genre_mapping = {
            "entertainment": "Drama",
            "comedy": "Comedy",
            "drama": "Drama",
            "documentary": "Documentary",
            "news": "News",
            "sports": "Sports",
            "reality": "Reality and Game Show",
            "animation": "Animation",
            "kids": "Kids and Family",
            "family": "Kids and Family",
            "music": "Music",
            "horror": "Horror",
            "talk": "Talk Show",
        }

        reach_genre = "Drama"  # Default
        for key, value in genre_mapping.items():
            if key.lower() in genre_code.lower():
                reach_genre = value
                break
        adi_json["reach_genre"]["value"] = reach_genre

    def to_adi_json(self) -> Dict[str, Dict[str, Any]]:
        """
        Convert the validated Bebanjo payload to adi_json format.

        Returns:
            Dictionary with RASCL adi_json structure where each field
            has a 'value' key containing the actual value.
        """
        dump = self.model_dump(by_alias=True, exclude_none=True)
        adi_json = {key: {"value": value} for key, value in dump.items()}

        self._set_adi_defaults(adi_json, dump)
        self._process_actors(adi_json)
        self._process_synopsis(adi_json, dump)
        self._map_reach_genre(adi_json)

        return adi_json
