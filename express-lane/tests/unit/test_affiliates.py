from collections import OrderedDict
from copy import deepcopy

import xmltodict

from src.task_config import task_config_assembler
from src.tasks import TaskAffiliateAssembler, Tasks


class TestAffiliates:
    def run_affiliate_test(self, global_variables, local_bucket) -> None:
        """
        Initializes all the services and configurations and runs the flow
        according to the configuration obtained.
        """

        event_settings_rules = global_variables.get("event_settings_rules")

        prev = local_bucket

        _raw_data = xmltodict.parse(prev, dict_constructor=OrderedDict)

        affiliates = (
            _raw_data.get("master", {}).get("affiliate_list", {}).get("affiliate", {})
        )

        # Ensure it's always a list
        if not isinstance(affiliates, list):
            affiliates = [affiliates]

        raw_data = deepcopy(_raw_data)

        filtered_affiliates = {}
        for k in affiliates:
            if k in event_settings_rules:
                filtered_affiliates[k] = event_settings_rules[k]

        global_variables["raw_data"] = raw_data

        task_config_assambler = task_config_assembler(
            global_variables=global_variables, filtered_affiliates=filtered_affiliates
        )

        global_variables["task_config"] = task_config_assambler

        affiliate_assambler = TaskAffiliateAssembler(
            global_variables=global_variables
        ).assemble()

        global_variables["affiliate_assambler"] = affiliate_assambler

        tasks = Tasks(global_variables)

        # STEP 1: Filter by Skip content
        tasks.skip_content()

        # STEP 2: Apply Category Rules
        tasks.apply_category_rules()

        # Step 3: Removed Attributes Rules
        tasks.removed_attributes()

        # Step 4: UPDATE ATTRIBUTES LOGIC
        tasks.update_attributes()

        # Step 5: REPLACE ATTRIBUTES LOGIC
        tasks.replace_attributes()

        # Step 6: Limit length
        tasks.limit_length()

        return global_variables

    def test_alta_fiber(self, global_variables, local_bucket):
        run_affiliate_test = self.run_affiliate_test(global_variables, local_bucket)

        affiliate_assambler = run_affiliate_test.get("affiliate_assambler")
        rules = run_affiliate_test.get("event_settings_rules")

        mvpd_list = (
            affiliate_assambler.get("clients", {}).get("rules", {}).get("mvpd", [])
        )

        delivery_name = "Alta_Fiber_Workflow"
        network_name = "National Geographic"
        category_value = "Free On Demand/By Channel/National Geographic/Crash Course Cuisine with Hudson Yang"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert ascp_client == delivery_name
                assert network_name not in rules.get("Alta Fiber").get(
                    "skip_content"
                ).get("values_skip")
                assert category_dict.get("@Value") == category_value
                assert tar == None

    def test_antietam(self, global_variables, local_bucket):
        run_affiliate_test = self.run_affiliate_test(global_variables, local_bucket)

        affiliate_assambler = run_affiliate_test.get("affiliate_assambler")
        rules = run_affiliate_test.get("event_settings_rules")

        mvpd_list = (
            affiliate_assambler.get("clients", {}).get("rules", {}).get("mvpd", [])
        )

        delivery_name = "Antietam_Cable_WG"
        category_value = "TV Shows/By Network/National Geographic/Crash Course HD"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@workgroup_name") == delivery_name:
                workgroup_name = matched_affiliate.get("@workgroup_name")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert workgroup_name == delivery_name
                assert [] == rules.get("Antietam").get("skip_content").get(
                    "values_skip"
                )
                assert category_dict.get("@Value") == category_value
                assert tar == None

    def test_armstrong(self, global_variables, local_bucket):
        run_affiliate_test = self.run_affiliate_test(global_variables, local_bucket)

        affiliate_assambler = run_affiliate_test.get("affiliate_assambler")
        rules = run_affiliate_test.get("event_settings_rules")

        mvpd_list = (
            affiliate_assambler.get("clients", {}).get("rules", {}).get("mvpd", [])
        )

        delivery_name = "Armstrong_Workflow"
        network_name = "National Geographic"
        # split from TV Networks/Nat Geo/Crash Course Cuisine with Hudson Yang|TV Series/Series A - F/Crash Course Cuisine with Hudson Yang
        category_value_from_source = [
            "TV Networks/Nat Geo/Crash Course Cuisine with Hudson Yang",
            "TV Series/Series A - F/Crash Course Cuisine with Hudson Yang",
        ]

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                ascp = matched_affiliate.get("ascp")
                tar = matched_affiliate.get("changes")
                category_values = [
                    d.get("@Value")
                    for d in matched_affiliate.get("ADI", {})
                    .get("Asset", {})
                    .get("Metadata", {})
                    .get("App_Data", [])
                    if d.get("@Name") == "Category"
                ]

                assert ascp_client == delivery_name
                assert network_name not in rules.get("Armstrong").get(
                    "skip_content"
                ).get("values_skip")
                assert category_values == category_value_from_source
                assert type(ascp) == dict
                assert list(ascp.keys()) == [
                    "host",
                    "port",
                    "username",
                    "pwd_or_key",
                    "authentication",
                    "target_rate",
                    "target",
                ]
                assert tar == None

    def test_astound_RCN(self, global_variables, local_bucket):
        delivery_name = "Astound_RCN_Workflow"

    def test_blue_Ridge(self, global_variables, local_bucket):
        delivery_name = "Blue_Ridge_Workflow"

    def test_blueStream_fiber(self, global_variables, local_bucket):
        delivery_name = "BlueStream_Fiber_Workflow"

    def test_breezeline(self, global_variables, local_bucket):
        delivery_name = ("Breezeline_Workflow",)

    def test_buckeye(self, global_variables, local_bucket):
        delivery_name = ("Buckeye_Workflow",)

    def test_C_spire(self, global_variables, local_bucket):
        delivery_name = ("C-Spire_Workflow",)

    def test_cablevision_altice(self, global_variables, local_bucket):
        delivery_name = ("Cablevision_Altice_Workflow",)
