from typing import Any

from utils.config import settings_config
from utils.LoggerService import logger_service
from utils.RetryHandler import <PERSON><PERSON><PERSON>and<PERSON>
from utils.schemas.dynamodb.bebanjo import BebanjoConfig
from utils.schemas.lambdas import EventSchema
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.SecretsManagerUtils import SecretsManagerUtils
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker

from .bebanjo_flow import flow


def lambda_handler(event: dict[str, Any], _: str) -> None:
    """
    Initializes all the services and configurations and runs the flow
    according to the configuration obtained.
    """

    logger_service.info("************ IZMA / lambda bebanjo started ************")
    logger_service.info("IZMA / lambda bebanjo with args: \n %s", event)

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    dynamodb_settings = settings_service.initialize("bebanjo")
    config: BebanjoConfig = dynamodb_settings.get_config()  # type: ignore
    status_table_name = dynamodb_settings.get_status_table()

    event_object = EventSchema(**event)  # type: ignore
    file_name_from_s3 = event_object.get_file_name_from_s3()
    bucket_name = event_object.get_bucket_name()

    status_tracker = StatusTrackerSchema(
        client_id=file_name_from_s3, landing_bucket=bucket_name
    )
    process_status_tracker = ProcessStatusTracker(status_table_name)
    process_status_tracker.create_initial_status(create_item=status_tracker)

    secrets = SecretsManagerUtils()
    bebanjo_secrets = secrets.get_secret_value(config.secret_key)
    global_variables = {
        "landing_bucket": bucket_name,
        "destination_bucket": config.destination_bucket,
        "file_name": file_name_from_s3,
        "secrets": bebanjo_secrets,
        "mapping_rules": dynamodb_settings.configuration_rules.rules.get(
            "mapping_rules_keys", []
        ),
    }

    try:
        process_status_tracker.mark_as_running(file_name_from_s3)

        flow(event_object, global_variables)

        process_status_tracker.mark_as_completed(file_name_from_s3)

    except BaseException as error:
        logger_service.error(
            "IZMA ERROR: %s, event_project: %s",
            error,
            dynamodb_settings.event_project_name,
        )
        error_message = f"{error}"
        process_status_tracker.mark_as_failed(file_name_from_s3, error_message)

        retry_handler = RetryHandler(dynamodb_settings.retries)
        retry_handler.execute(flow, dynamodb_settings, global_variables)

    logger_service.info("IZMA / lambda bebanjo finished")


if __name__ == "__main__":
    lambda_handler(
        {
            "Records": [
                {
                    "s3": {
                        "object": {
                            "key": "TIER 1 VOD ABC HD LF C7-mass0000000002805925-20250603T135743464.json",
                        },
                        "bucket": {"name": "reachengine-nldschedules-sbx"},
                    }
                }
            ]
        },
        "",
    )
